import { NextRequest, NextResponse } from 'next/server'
import { dataAccessManager } from '@/services/dataAccess/DataAccessManager'

/**
 * 生成安全密码API
 * POST /api/users/generate-password - 生成安全密码
 */

export async function POST(request: NextRequest) {
  try {
    const { length }: { length?: number } = await request.json()

    const response = await dataAccessManager.auth.generateSecurePassword(length || 12)

    if (response.status === 'success') {
      return NextResponse.json({
        success: true,
        data: response.data,
        message: response.message
      })
    } else {
      return NextResponse.json(
        {
          success: false,
          error: response.message,
          code: response.code
        },
        { status: 400 }
      )
    }
  } catch (error) {
    console.error('生成密码失败:', error)
    return NextResponse.json(
      {
        success: false,
        error: '生成密码失败',
        details: error instanceof Error ? error.message : '未知错误'
      },
      { status: 500 }
    )
  }
}
