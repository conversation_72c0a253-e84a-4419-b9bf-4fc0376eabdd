import { NextRequest, NextResponse } from 'next/server'
import { dataAccessManager } from '@/services/dataAccess/DataAccessManager'

/**
 * 角色权限分配API
 * GET /api/roles/[id]/permissions - 获取角色权限
 * PUT /api/roles/[id]/permissions - 分配角色权限
 */

export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const { id } = params

    const response = await dataAccessManager.roles.getById(id)

    if (response.status === 'success') {
      return NextResponse.json({
        success: true,
        data: {
          roleId: id,
          permissions: response.data?.permissions || []
        },
        message: '获取角色权限成功'
      })
    } else {
      return NextResponse.json(
        {
          success: false,
          error: response.message,
          code: response.code
        },
        { status: response.code === 'ROLE_NOT_FOUND' ? 404 : 400 }
      )
    }
  } catch (error) {
    console.error('获取角色权限失败:', error)
    return NextResponse.json(
      {
        success: false,
        error: '获取角色权限失败',
        details: error instanceof Error ? error.message : '未知错误'
      },
      { status: 500 }
    )
  }
}

export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const { id } = params
    const { permissionIds }: { permissionIds: string[] } = await request.json()

    if (!permissionIds || !Array.isArray(permissionIds)) {
      return NextResponse.json(
        {
          success: false,
          error: '权限ID列表格式不正确'
        },
        { status: 400 }
      )
    }

    const response = await dataAccessManager.roles.assignPermissions(id, permissionIds)

    if (response.status === 'success') {
      return NextResponse.json({
        success: true,
        data: response.data,
        message: response.message
      })
    } else {
      return NextResponse.json(
        {
          success: false,
          error: response.message,
          code: response.code
        },
        { status: response.code === 'ROLE_NOT_FOUND' ? 404 : 400 }
      )
    }
  } catch (error) {
    console.error('分配角色权限失败:', error)
    return NextResponse.json(
      {
        success: false,
        error: '分配角色权限失败',
        details: error instanceof Error ? error.message : '未知错误'
      },
      { status: 500 }
    )
  }
}
