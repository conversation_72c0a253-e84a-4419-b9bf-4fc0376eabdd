import { NextRequest, NextResponse } from 'next/server'
import { dataAccessManager } from '@/services/dataAccess/DataAccessManager'
import { CreateUserRequest, UserStatus } from '@/types/auth'

/**
 * 用户管理API
 * GET /api/users - 获取用户列表
 * POST /api/users - 创建新用户
 */

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const page = parseInt(searchParams.get('page') || '1')
    const pageSize = parseInt(searchParams.get('pageSize') || '10')
    const search = searchParams.get('search') || undefined
    const status = searchParams.get('status') as UserStatus || undefined

    const response = await dataAccessManager.auth.getUsersList({
      page,
      pageSize,
      search,
      status
    })

    if (response.status === 'success') {
      return NextResponse.json({
        success: true,
        data: response.data,
        message: response.message
      })
    } else {
      return NextResponse.json(
        {
          success: false,
          error: response.message,
          code: response.code
        },
        { status: 400 }
      )
    }
  } catch (error) {
    console.error('获取用户列表失败:', error)
    return NextResponse.json(
      {
        success: false,
        error: '获取用户列表失败',
        details: error instanceof Error ? error.message : '未知错误'
      },
      { status: 500 }
    )
  }
}

export async function POST(request: NextRequest) {
  try {
    const userData: CreateUserRequest = await request.json()

    // 基本验证
    if (!userData.username || !userData.password || !userData.fullName) {
      return NextResponse.json(
        {
          success: false,
          error: '用户名、密码和姓名为必填项'
        },
        { status: 400 }
      )
    }

    if (!userData.roleIds || userData.roleIds.length === 0) {
      return NextResponse.json(
        {
          success: false,
          error: '必须为用户分配至少一个角色'
        },
        { status: 400 }
      )
    }

    const response = await dataAccessManager.auth.createUser(userData)

    if (response.status === 'success') {
      return NextResponse.json({
        success: true,
        data: response.data,
        message: response.message
      })
    } else {
      return NextResponse.json(
        {
          success: false,
          error: response.message,
          code: response.code
        },
        { status: 400 }
      )
    }
  } catch (error) {
    console.error('创建用户失败:', error)
    return NextResponse.json(
      {
        success: false,
        error: '创建用户失败',
        details: error instanceof Error ? error.message : '未知错误'
      },
      { status: 500 }
    )
  }
}
