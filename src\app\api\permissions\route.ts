import { NextRequest, NextResponse } from 'next/server'
import { dataAccessManager } from '@/services/dataAccess/DataAccessManager'

/**
 * 权限管理API
 * GET /api/permissions - 获取权限列表
 */

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const module = searchParams.get('module')

    let response
    if (module) {
      response = await dataAccessManager.roles.getPermissionsByModule(module)
    } else {
      response = await dataAccessManager.roles.getPermissions()
    }

    if (response.status === 'success') {
      return NextResponse.json({
        success: true,
        data: response.data,
        message: response.message
      })
    } else {
      return NextResponse.json(
        {
          success: false,
          error: response.message,
          code: response.code
        },
        { status: 400 }
      )
    }
  } catch (error) {
    console.error('获取权限列表失败:', error)
    return NextResponse.json(
      {
        success: false,
        error: '获取权限列表失败',
        details: error instanceof Error ? error.message : '未知错误'
      },
      { status: 500 }
    )
  }
}
