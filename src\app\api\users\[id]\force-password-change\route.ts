import { NextRequest, NextResponse } from 'next/server'
import { dataAccessManager } from '@/services/dataAccess/DataAccessManager'

/**
 * 强制修改密码API
 * PUT /api/users/[id]/force-password-change - 设置用户强制修改密码
 */

export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const { id } = params
    const { force }: { force: boolean } = await request.json()

    const response = await dataAccessManager.auth.forcePasswordChange(id, force)

    if (response.status === 'success') {
      return NextResponse.json({
        success: true,
        data: response.data,
        message: response.message
      })
    } else {
      return NextResponse.json(
        {
          success: false,
          error: response.message,
          code: response.code
        },
        { status: response.code === 'USER_NOT_FOUND' ? 404 : 400 }
      )
    }
  } catch (error) {
    console.error('设置强制修改密码失败:', error)
    return NextResponse.json(
      {
        success: false,
        error: '设置强制修改密码失败',
        details: error instanceof Error ? error.message : '未知错误'
      },
      { status: 500 }
    )
  }
}
