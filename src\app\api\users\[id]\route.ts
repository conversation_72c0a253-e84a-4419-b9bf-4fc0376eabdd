import { NextRequest, NextResponse } from 'next/server'
import { dataAccessManager } from '@/services/dataAccess/DataAccessManager'
import { UpdateUserRequest } from '@/types/auth'

/**
 * 单个用户管理API
 * GET /api/users/[id] - 获取用户详情
 * PUT /api/users/[id] - 更新用户信息
 * DELETE /api/users/[id] - 删除用户
 */

export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const { id } = params

    const response = await dataAccessManager.auth.getUserById(id)

    if (response.status === 'success') {
      return NextResponse.json({
        success: true,
        data: response.data,
        message: response.message
      })
    } else {
      return NextResponse.json(
        {
          success: false,
          error: response.message,
          code: response.code
        },
        { status: response.code === 'USER_NOT_FOUND' ? 404 : 400 }
      )
    }
  } catch (error) {
    console.error('获取用户详情失败:', error)
    return NextResponse.json(
      {
        success: false,
        error: '获取用户详情失败',
        details: error instanceof Error ? error.message : '未知错误'
      },
      { status: 500 }
    )
  }
}

export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const { id } = params
    const updates: UpdateUserRequest = await request.json()

    const response = await dataAccessManager.auth.updateUser(id, updates)

    if (response.status === 'success') {
      return NextResponse.json({
        success: true,
        data: response.data,
        message: response.message
      })
    } else {
      return NextResponse.json(
        {
          success: false,
          error: response.message,
          code: response.code
        },
        { status: response.code === 'USER_NOT_FOUND' ? 404 : 400 }
      )
    }
  } catch (error) {
    console.error('更新用户失败:', error)
    return NextResponse.json(
      {
        success: false,
        error: '更新用户失败',
        details: error instanceof Error ? error.message : '未知错误'
      },
      { status: 500 }
    )
  }
}

export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const { id } = params

    const response = await dataAccessManager.auth.deleteUser(id)

    if (response.status === 'success') {
      return NextResponse.json({
        success: true,
        data: response.data,
        message: response.message
      })
    } else {
      return NextResponse.json(
        {
          success: false,
          error: response.message,
          code: response.code
        },
        { status: response.code === 'USER_NOT_FOUND' ? 404 : 400 }
      )
    }
  } catch (error) {
    console.error('删除用户失败:', error)
    return NextResponse.json(
      {
        success: false,
        error: '删除用户失败',
        details: error instanceof Error ? error.message : '未知错误'
      },
      { status: 500 }
    )
  }
}
