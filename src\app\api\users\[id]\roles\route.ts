import { NextRequest, NextResponse } from 'next/server'
import { dataAccessManager } from '@/services/dataAccess/DataAccessManager'

/**
 * 用户角色管理API
 * GET /api/users/[id]/roles - 获取用户角色
 * PUT /api/users/[id]/roles - 分配用户角色
 */

export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const { id } = params

    const response = await dataAccessManager.auth.getUserById(id)

    if (response.status === 'success') {
      return NextResponse.json({
        success: true,
        data: {
          userId: id,
          roles: response.data?.roles || []
        },
        message: '获取用户角色成功'
      })
    } else {
      return NextResponse.json(
        {
          success: false,
          error: response.message,
          code: response.code
        },
        { status: response.code === 'USER_NOT_FOUND' ? 404 : 400 }
      )
    }
  } catch (error) {
    console.error('获取用户角色失败:', error)
    return NextResponse.json(
      {
        success: false,
        error: '获取用户角色失败',
        details: error instanceof Error ? error.message : '未知错误'
      },
      { status: 500 }
    )
  }
}

export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const { id } = params
    const { roleIds }: { roleIds: string[] } = await request.json()

    if (!roleIds || !Array.isArray(roleIds)) {
      return NextResponse.json(
        {
          success: false,
          error: '角色ID列表格式不正确'
        },
        { status: 400 }
      )
    }

    const response = await dataAccessManager.auth.assignUserRoles(id, roleIds)

    if (response.status === 'success') {
      return NextResponse.json({
        success: true,
        data: response.data,
        message: response.message
      })
    } else {
      return NextResponse.json(
        {
          success: false,
          error: response.message,
          code: response.code
        },
        { status: response.code === 'USER_NOT_FOUND' ? 404 : 400 }
      )
    }
  } catch (error) {
    console.error('分配用户角色失败:', error)
    return NextResponse.json(
      {
        success: false,
        error: '分配用户角色失败',
        details: error instanceof Error ? error.message : '未知错误'
      },
      { status: 500 }
    )
  }
}
