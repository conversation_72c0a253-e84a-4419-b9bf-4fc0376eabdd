import { NextRequest, NextResponse } from 'next/server'
import { dataAccessManager } from '@/services/dataAccess/DataAccessManager'

/**
 * 用户密码管理API
 * PUT /api/users/[id]/password - 重置用户密码
 */

export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const { id } = params
    const { newPassword }: { newPassword: string } = await request.json()

    if (!newPassword) {
      return NextResponse.json(
        {
          success: false,
          error: '新密码不能为空'
        },
        { status: 400 }
      )
    }

    const response = await dataAccessManager.auth.resetPassword(id, newPassword)

    if (response.status === 'success') {
      return NextResponse.json({
        success: true,
        data: response.data,
        message: response.message
      })
    } else {
      return NextResponse.json(
        {
          success: false,
          error: response.message,
          code: response.code
        },
        { status: response.code === 'USER_NOT_FOUND' ? 404 : 400 }
      )
    }
  } catch (error) {
    console.error('重置密码失败:', error)
    return NextResponse.json(
      {
        success: false,
        error: '重置密码失败',
        details: error instanceof Error ? error.message : '未知错误'
      },
      { status: 500 }
    )
  }
}
