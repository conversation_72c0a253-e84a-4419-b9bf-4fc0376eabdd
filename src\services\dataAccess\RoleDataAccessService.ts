/**
 * 角色管理数据访问服务
 * 
 * 负责角色和权限相关的数据访问操作
 * 遵循DataAccessManager的架构规范
 */

import { 
  Role, 
  Permission, 
  ApiResponse,
  PermissionType
} from '@/types/auth'

/**
 * 角色创建请求接口
 */
export interface CreateRoleRequest {
  code: string
  name: string
  description?: string
  permissionIds: string[]
}

/**
 * 角色更新请求接口
 */
export interface UpdateRoleRequest {
  name?: string
  description?: string
  permissionIds?: string[]
}

/**
 * 角色数据访问接口
 */
export interface IRoleDataAccess {
  // 角色管理
  getRoles(): Promise<ApiResponse<Role[]>>
  getRoleById(id: string): Promise<ApiResponse<Role>>
  getRoleByCode(code: string): Promise<ApiResponse<Role>>
  createRole(roleData: CreateRoleRequest): Promise<ApiResponse<Role>>
  updateRole(id: string, updates: UpdateRoleRequest): Promise<ApiResponse<Role>>
  deleteRole(id: string): Promise<ApiResponse<boolean>>
  
  // 权限管理
  getPermissions(): Promise<ApiResponse<Permission[]>>
  getPermissionsByModule(module: string): Promise<ApiResponse<Permission[]>>
  assignRolePermissions(roleId: string, permissionIds: string[]): Promise<ApiResponse<Role>>
}

/**
 * 模拟数据存储
 */
class MockRoleDataStore {
  private static roles: Role[] = [
    {
      id: 'role-admin',
      code: 'admin',
      name: '系统管理员',
      description: '拥有系统所有权限',
      permissions: [
        {
          id: 'perm-system-admin',
          code: 'system:admin',
          name: '系统管理',
          type: 'module',
          resource: 'system',
          action: 'admin',
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString()
        },
        {
          id: 'perm-users-manage',
          code: 'admin:users:*',
          name: '用户管理',
          type: 'module',
          resource: 'users',
          action: '*',
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString()
        },
        {
          id: 'perm-roles-manage',
          code: 'admin:roles:*',
          name: '角色管理',
          type: 'module',
          resource: 'roles',
          action: '*',
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString()
        }
      ],
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    },
    {
      id: 'role-manager',
      code: 'manager',
      name: '管理员',
      description: '业务管理权限',
      permissions: [
        {
          id: 'perm-sales-manage',
          code: 'sales:*',
          name: '销售管理',
          type: 'module',
          resource: 'sales',
          action: '*',
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString()
        },
        {
          id: 'perm-production-manage',
          code: 'production:*',
          name: '生产管理',
          type: 'module',
          resource: 'production',
          action: '*',
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString()
        }
      ],
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    },
    {
      id: 'role-user',
      code: 'user',
      name: '普通用户',
      description: '基础查看权限',
      permissions: [
        {
          id: 'perm-sales-read',
          code: 'sales:read',
          name: '查看销售数据',
          type: 'operation',
          resource: 'sales',
          action: 'read',
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString()
        },
        {
          id: 'perm-production-read',
          code: 'production:read',
          name: '查看生产数据',
          type: 'operation',
          resource: 'production',
          action: 'read',
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString()
        }
      ],
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    }
  ]

  private static permissions: Permission[] = [
    // 系统管理权限
    {
      id: 'perm-system-admin',
      code: 'system:admin',
      name: '系统管理',
      description: '系统管理员权限',
      type: 'module',
      resource: 'system',
      action: 'admin',
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    },
    // 用户管理权限
    {
      id: 'perm-users-read',
      code: 'admin:users:read',
      name: '查看用户',
      description: '查看用户列表和详情',
      type: 'operation',
      resource: 'users',
      action: 'read',
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    },
    {
      id: 'perm-users-create',
      code: 'admin:users:create',
      name: '创建用户',
      description: '创建新用户',
      type: 'operation',
      resource: 'users',
      action: 'create',
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    },
    {
      id: 'perm-users-update',
      code: 'admin:users:update',
      name: '更新用户',
      description: '更新用户信息',
      type: 'operation',
      resource: 'users',
      action: 'update',
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    },
    {
      id: 'perm-users-delete',
      code: 'admin:users:delete',
      name: '删除用户',
      description: '删除用户',
      type: 'operation',
      resource: 'users',
      action: 'delete',
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    },
    {
      id: 'perm-users-manage',
      code: 'admin:users:*',
      name: '用户管理',
      description: '用户管理所有权限',
      type: 'module',
      resource: 'users',
      action: '*',
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    },
    // 角色管理权限
    {
      id: 'perm-roles-read',
      code: 'admin:roles:read',
      name: '查看角色',
      description: '查看角色列表和详情',
      type: 'operation',
      resource: 'roles',
      action: 'read',
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    },
    {
      id: 'perm-roles-create',
      code: 'admin:roles:create',
      name: '创建角色',
      description: '创建新角色',
      type: 'operation',
      resource: 'roles',
      action: 'create',
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    },
    {
      id: 'perm-roles-update',
      code: 'admin:roles:update',
      name: '更新角色',
      description: '更新角色信息',
      type: 'operation',
      resource: 'roles',
      action: 'update',
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    },
    {
      id: 'perm-roles-delete',
      code: 'admin:roles:delete',
      name: '删除角色',
      description: '删除角色',
      type: 'operation',
      resource: 'roles',
      action: 'delete',
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    },
    {
      id: 'perm-roles-manage',
      code: 'admin:roles:*',
      name: '角色管理',
      description: '角色管理所有权限',
      type: 'module',
      resource: 'roles',
      action: '*',
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    }
  ]

  static getRoles(): Role[] {
    return [...this.roles]
  }

  static getRoleById(id: string): Role | undefined {
    return this.roles.find(role => role.id === id)
  }

  static getRoleByCode(code: string): Role | undefined {
    return this.roles.find(role => role.code === code)
  }

  static addRole(role: Role): void {
    this.roles.push(role)
  }

  static updateRole(id: string, updates: Partial<Role>): boolean {
    const index = this.roles.findIndex(role => role.id === id)
    if (index !== -1) {
      this.roles[index] = { ...this.roles[index], ...updates, updatedAt: new Date().toISOString() }
      return true
    }
    return false
  }

  static deleteRole(id: string): boolean {
    const index = this.roles.findIndex(role => role.id === id)
    if (index !== -1) {
      this.roles.splice(index, 1)
      return true
    }
    return false
  }

  static getPermissions(): Permission[] {
    return [...this.permissions]
  }

  static getPermissionById(id: string): Permission | undefined {
    return this.permissions.find(permission => permission.id === id)
  }

  static getPermissionsByIds(ids: string[]): Permission[] {
    return this.permissions.filter(permission => ids.includes(permission.id))
  }

  static getPermissionsByModule(module: string): Permission[] {
    return this.permissions.filter(permission => permission.resource === module)
  }
}

/**
 * 角色数据访问服务实现
 */
export class RoleDataAccessService implements IRoleDataAccess {
  private generateRequestId(): string {
    return `req_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
  }

  async getRoles(): Promise<ApiResponse<Role[]>> {
    try {
      const roles = MockRoleDataStore.getRoles()
      return {
        status: 'success',
        data: roles,
        message: '获取角色列表成功',
        code: 'GET_ROLES_SUCCESS',
        timestamp: new Date().toISOString(),
        requestId: this.generateRequestId()
      }
    } catch (error) {
      return {
        status: 'error',
        data: null,
        message: error instanceof Error ? error.message : '获取角色列表失败',
        code: 'GET_ROLES_ERROR',
        timestamp: new Date().toISOString(),
        requestId: this.generateRequestId()
      }
    }
  }

  async getRoleById(id: string): Promise<ApiResponse<Role>> {
    try {
      const role = MockRoleDataStore.getRoleById(id)
      if (!role) {
        return {
          status: 'error',
          data: null,
          message: '角色不存在',
          code: 'ROLE_NOT_FOUND',
          timestamp: new Date().toISOString(),
          requestId: this.generateRequestId()
        }
      }

      return {
        status: 'success',
        data: role,
        message: '获取角色详情成功',
        code: 'GET_ROLE_SUCCESS',
        timestamp: new Date().toISOString(),
        requestId: this.generateRequestId()
      }
    } catch (error) {
      return {
        status: 'error',
        data: null,
        message: error instanceof Error ? error.message : '获取角色详情失败',
        code: 'GET_ROLE_ERROR',
        timestamp: new Date().toISOString(),
        requestId: this.generateRequestId()
      }
    }
  }

  async getRoleByCode(code: string): Promise<ApiResponse<Role>> {
    try {
      const role = MockRoleDataStore.getRoleByCode(code)
      if (!role) {
        return {
          status: 'error',
          data: null,
          message: '角色不存在',
          code: 'ROLE_NOT_FOUND',
          timestamp: new Date().toISOString(),
          requestId: this.generateRequestId()
        }
      }

      return {
        status: 'success',
        data: role,
        message: '获取角色详情成功',
        code: 'GET_ROLE_SUCCESS',
        timestamp: new Date().toISOString(),
        requestId: this.generateRequestId()
      }
    } catch (error) {
      return {
        status: 'error',
        data: null,
        message: error instanceof Error ? error.message : '获取角色详情失败',
        code: 'GET_ROLE_ERROR',
        timestamp: new Date().toISOString(),
        requestId: this.generateRequestId()
      }
    }
  }

  async createRole(roleData: CreateRoleRequest): Promise<ApiResponse<Role>> {
    try {
      // 验证角色代码是否已存在
      const existingRole = MockRoleDataStore.getRoleByCode(roleData.code)
      if (existingRole) {
        return {
          status: 'error',
          data: null,
          message: '角色代码已存在',
          code: 'ROLE_CODE_EXISTS',
          timestamp: new Date().toISOString(),
          requestId: this.generateRequestId()
        }
      }

      // 获取权限信息
      const permissions = MockRoleDataStore.getPermissionsByIds(roleData.permissionIds)

      // 生成角色ID
      const roleId = `role-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`

      // 创建角色对象
      const newRole: Role = {
        id: roleId,
        code: roleData.code,
        name: roleData.name,
        description: roleData.description,
        permissions,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      }

      // 保存角色
      MockRoleDataStore.addRole(newRole)

      return {
        status: 'success',
        data: newRole,
        message: '角色创建成功',
        code: 'CREATE_ROLE_SUCCESS',
        timestamp: new Date().toISOString(),
        requestId: this.generateRequestId()
      }
    } catch (error) {
      return {
        status: 'error',
        data: null,
        message: error instanceof Error ? error.message : '创建角色失败',
        code: 'CREATE_ROLE_ERROR',
        timestamp: new Date().toISOString(),
        requestId: this.generateRequestId()
      }
    }
  }

  async updateRole(id: string, updates: UpdateRoleRequest): Promise<ApiResponse<Role>> {
    try {
      const existingRole = MockRoleDataStore.getRoleById(id)
      if (!existingRole) {
        return {
          status: 'error',
          data: null,
          message: '角色不存在',
          code: 'ROLE_NOT_FOUND',
          timestamp: new Date().toISOString(),
          requestId: this.generateRequestId()
        }
      }

      // 检查是否为系统角色（不允许修改）
      if (existingRole.code === 'admin') {
        return {
          status: 'error',
          data: null,
          message: '不能修改系统管理员角色',
          code: 'CANNOT_UPDATE_SYSTEM_ROLE',
          timestamp: new Date().toISOString(),
          requestId: this.generateRequestId()
        }
      }

      // 处理权限更新
      let updatedPermissions = existingRole.permissions
      if (updates.permissionIds) {
        updatedPermissions = MockRoleDataStore.getPermissionsByIds(updates.permissionIds)
      }

      // 更新角色信息
      const updateData = {
        ...updates,
        permissions: updatedPermissions,
        updatedAt: new Date().toISOString()
      }

      const success = MockRoleDataStore.updateRole(id, updateData)
      if (!success) {
        return {
          status: 'error',
          data: null,
          message: '更新角色失败',
          code: 'UPDATE_ROLE_ERROR',
          timestamp: new Date().toISOString(),
          requestId: this.generateRequestId()
        }
      }

      const updatedRole = MockRoleDataStore.getRoleById(id)!
      return {
        status: 'success',
        data: updatedRole,
        message: '角色更新成功',
        code: 'UPDATE_ROLE_SUCCESS',
        timestamp: new Date().toISOString(),
        requestId: this.generateRequestId()
      }
    } catch (error) {
      return {
        status: 'error',
        data: null,
        message: error instanceof Error ? error.message : '更新角色失败',
        code: 'UPDATE_ROLE_ERROR',
        timestamp: new Date().toISOString(),
        requestId: this.generateRequestId()
      }
    }
  }

  async deleteRole(id: string): Promise<ApiResponse<boolean>> {
    try {
      const existingRole = MockRoleDataStore.getRoleById(id)
      if (!existingRole) {
        return {
          status: 'error',
          data: false,
          message: '角色不存在',
          code: 'ROLE_NOT_FOUND',
          timestamp: new Date().toISOString(),
          requestId: this.generateRequestId()
        }
      }

      // 检查是否为系统角色（不允许删除）
      if (existingRole.code === 'admin') {
        return {
          status: 'error',
          data: false,
          message: '不能删除系统管理员角色',
          code: 'CANNOT_DELETE_SYSTEM_ROLE',
          timestamp: new Date().toISOString(),
          requestId: this.generateRequestId()
        }
      }

      const success = MockRoleDataStore.deleteRole(id)
      return {
        status: 'success',
        data: success,
        message: success ? '角色删除成功' : '角色删除失败',
        code: success ? 'DELETE_ROLE_SUCCESS' : 'DELETE_ROLE_ERROR',
        timestamp: new Date().toISOString(),
        requestId: this.generateRequestId()
      }
    } catch (error) {
      return {
        status: 'error',
        data: false,
        message: error instanceof Error ? error.message : '删除角色失败',
        code: 'DELETE_ROLE_ERROR',
        timestamp: new Date().toISOString(),
        requestId: this.generateRequestId()
      }
    }
  }

  async getPermissions(): Promise<ApiResponse<Permission[]>> {
    try {
      const permissions = MockRoleDataStore.getPermissions()
      return {
        status: 'success',
        data: permissions,
        message: '获取权限列表成功',
        code: 'GET_PERMISSIONS_SUCCESS',
        timestamp: new Date().toISOString(),
        requestId: this.generateRequestId()
      }
    } catch (error) {
      return {
        status: 'error',
        data: null,
        message: error instanceof Error ? error.message : '获取权限列表失败',
        code: 'GET_PERMISSIONS_ERROR',
        timestamp: new Date().toISOString(),
        requestId: this.generateRequestId()
      }
    }
  }

  async getPermissionsByModule(module: string): Promise<ApiResponse<Permission[]>> {
    try {
      const permissions = MockRoleDataStore.getPermissionsByModule(module)
      return {
        status: 'success',
        data: permissions,
        message: '获取模块权限成功',
        code: 'GET_MODULE_PERMISSIONS_SUCCESS',
        timestamp: new Date().toISOString(),
        requestId: this.generateRequestId()
      }
    } catch (error) {
      return {
        status: 'error',
        data: null,
        message: error instanceof Error ? error.message : '获取模块权限失败',
        code: 'GET_MODULE_PERMISSIONS_ERROR',
        timestamp: new Date().toISOString(),
        requestId: this.generateRequestId()
      }
    }
  }

  async assignRolePermissions(roleId: string, permissionIds: string[]): Promise<ApiResponse<Role>> {
    try {
      const role = MockRoleDataStore.getRoleById(roleId)
      if (!role) {
        return {
          status: 'error',
          data: null,
          message: '角色不存在',
          code: 'ROLE_NOT_FOUND',
          timestamp: new Date().toISOString(),
          requestId: this.generateRequestId()
        }
      }

      // 获取权限信息
      const permissions = MockRoleDataStore.getPermissionsByIds(permissionIds)

      // 更新角色权限
      const success = MockRoleDataStore.updateRole(roleId, {
        permissions,
        updatedAt: new Date().toISOString()
      })

      if (!success) {
        return {
          status: 'error',
          data: null,
          message: '权限分配失败',
          code: 'ASSIGN_PERMISSIONS_ERROR',
          timestamp: new Date().toISOString(),
          requestId: this.generateRequestId()
        }
      }

      const updatedRole = MockRoleDataStore.getRoleById(roleId)!
      return {
        status: 'success',
        data: updatedRole,
        message: '权限分配成功',
        code: 'ASSIGN_PERMISSIONS_SUCCESS',
        timestamp: new Date().toISOString(),
        requestId: this.generateRequestId()
      }
    } catch (error) {
      return {
        status: 'error',
        data: null,
        message: error instanceof Error ? error.message : '权限分配失败',
        code: 'ASSIGN_PERMISSIONS_ERROR',
        timestamp: new Date().toISOString(),
        requestId: this.generateRequestId()
      }
    }
  }
}
