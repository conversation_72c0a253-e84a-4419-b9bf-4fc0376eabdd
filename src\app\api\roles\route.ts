import { NextRequest, NextResponse } from 'next/server'
import { dataAccessManager } from '@/services/dataAccess/DataAccessManager'
import { CreateRoleRequest } from '@/services/dataAccess/RoleDataAccessService'

/**
 * 角色管理API
 * GET /api/roles - 获取角色列表
 * POST /api/roles - 创建新角色
 */

export async function GET(request: NextRequest) {
  try {
    const response = await dataAccessManager.roles.getAll()

    if (response.status === 'success') {
      return NextResponse.json({
        success: true,
        data: response.data,
        message: response.message
      })
    } else {
      return NextResponse.json(
        {
          success: false,
          error: response.message,
          code: response.code
        },
        { status: 400 }
      )
    }
  } catch (error) {
    console.error('获取角色列表失败:', error)
    return NextResponse.json(
      {
        success: false,
        error: '获取角色列表失败',
        details: error instanceof Error ? error.message : '未知错误'
      },
      { status: 500 }
    )
  }
}

export async function POST(request: NextRequest) {
  try {
    const roleData: CreateRoleRequest = await request.json()

    // 基本验证
    if (!roleData.code || !roleData.name) {
      return NextResponse.json(
        {
          success: false,
          error: '角色代码和名称为必填项'
        },
        { status: 400 }
      )
    }

    if (!roleData.permissionIds || roleData.permissionIds.length === 0) {
      return NextResponse.json(
        {
          success: false,
          error: '必须为角色分配至少一个权限'
        },
        { status: 400 }
      )
    }

    const response = await dataAccessManager.roles.create(roleData)

    if (response.status === 'success') {
      return NextResponse.json({
        success: true,
        data: response.data,
        message: response.message
      })
    } else {
      return NextResponse.json(
        {
          success: false,
          error: response.message,
          code: response.code
        },
        { status: 400 }
      )
    }
  } catch (error) {
    console.error('创建角色失败:', error)
    return NextResponse.json(
      {
        success: false,
        error: '创建角色失败',
        details: error instanceof Error ? error.message : '未知错误'
      },
      { status: 500 }
    )
  }
}
