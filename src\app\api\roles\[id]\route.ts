import { NextRequest, NextResponse } from 'next/server'
import { dataAccessManager } from '@/services/dataAccess/DataAccessManager'
import { UpdateRoleRequest } from '@/services/dataAccess/RoleDataAccessService'

/**
 * 单个角色管理API
 * GET /api/roles/[id] - 获取角色详情
 * PUT /api/roles/[id] - 更新角色信息
 * DELETE /api/roles/[id] - 删除角色
 */

export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const { id } = params

    const response = await dataAccessManager.roles.getById(id)

    if (response.status === 'success') {
      return NextResponse.json({
        success: true,
        data: response.data,
        message: response.message
      })
    } else {
      return NextResponse.json(
        {
          success: false,
          error: response.message,
          code: response.code
        },
        { status: response.code === 'ROLE_NOT_FOUND' ? 404 : 400 }
      )
    }
  } catch (error) {
    console.error('获取角色详情失败:', error)
    return NextResponse.json(
      {
        success: false,
        error: '获取角色详情失败',
        details: error instanceof Error ? error.message : '未知错误'
      },
      { status: 500 }
    )
  }
}

export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const { id } = params
    const updates: UpdateRoleRequest = await request.json()

    const response = await dataAccessManager.roles.update(id, updates)

    if (response.status === 'success') {
      return NextResponse.json({
        success: true,
        data: response.data,
        message: response.message
      })
    } else {
      return NextResponse.json(
        {
          success: false,
          error: response.message,
          code: response.code
        },
        { status: response.code === 'ROLE_NOT_FOUND' ? 404 : 400 }
      )
    }
  } catch (error) {
    console.error('更新角色失败:', error)
    return NextResponse.json(
      {
        success: false,
        error: '更新角色失败',
        details: error instanceof Error ? error.message : '未知错误'
      },
      { status: 500 }
    )
  }
}

export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const { id } = params

    const response = await dataAccessManager.roles.delete(id)

    if (response.status === 'success') {
      return NextResponse.json({
        success: true,
        data: response.data,
        message: response.message
      })
    } else {
      return NextResponse.json(
        {
          success: false,
          error: response.message,
          code: response.code
        },
        { status: response.code === 'ROLE_NOT_FOUND' ? 404 : 400 }
      )
    }
  } catch (error) {
    console.error('删除角色失败:', error)
    return NextResponse.json(
      {
        success: false,
        error: '删除角色失败',
        details: error instanceof Error ? error.message : '未知错误'
      },
      { status: 500 }
    )
  }
}
