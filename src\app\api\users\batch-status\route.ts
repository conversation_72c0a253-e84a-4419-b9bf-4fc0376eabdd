import { NextRequest, NextResponse } from 'next/server'
import { dataAccessManager } from '@/services/dataAccess/DataAccessManager'
import { UserStatus } from '@/types/auth'

/**
 * 批量用户状态管理API
 * PUT /api/users/batch-status - 批量更新用户状态
 */

export async function PUT(request: NextRequest) {
  try {
    const { userIds, status }: { userIds: string[], status: UserStatus } = await request.json()

    // 基本验证
    if (!userIds || !Array.isArray(userIds) || userIds.length === 0) {
      return NextResponse.json(
        {
          success: false,
          error: '用户ID列表不能为空'
        },
        { status: 400 }
      )
    }

    if (!status || !['active', 'inactive', 'locked'].includes(status)) {
      return NextResponse.json(
        {
          success: false,
          error: '无效的用户状态'
        },
        { status: 400 }
      )
    }

    const response = await dataAccessManager.auth.batchUpdateUserStatus(userIds, status)

    if (response.status === 'success') {
      return NextResponse.json({
        success: true,
        data: response.data,
        message: response.message
      })
    } else {
      return NextResponse.json(
        {
          success: false,
          error: response.message,
          code: response.code
        },
        { status: 400 }
      )
    }
  } catch (error) {
    console.error('批量更新用户状态失败:', error)
    return NextResponse.json(
      {
        success: false,
        error: '批量更新用户状态失败',
        details: error instanceof Error ? error.message : '未知错误'
      },
      { status: 500 }
    )
  }
}
